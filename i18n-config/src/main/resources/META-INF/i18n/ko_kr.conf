logo = [
  ""
  ""
  "    §1    __                    _                       "
  "    §9   / /   __  ______ ___  (_)___  ____ __________ _"
  "    §3  / /   / / / / __ `__ \\/ / __ \\/ __ `/ ___/ __ `/"
  "    §6 / /___/ /_/ / / / / / / / / / / /_/ / /  / /_/ / "
  "    §e/_____/\\__,_/_/ /_/ /_/_/_/ /_/\\__,_/_/   \\__,_/  "
  ""
  "    §eLuminara·流明纳拉 서버 제작자§b QianMoo0121(QianMo_ProMax)"
  "    §a버전: {} / {}"
  "    §a빌드 날짜: {}"
  ""
]
# Translate the word in parenthesis only.
# If there's same Chinese word with same meaning in your language(i.e. Kanji in Japanese), then remove the parenthesis.
# Note_KOR: Korea once used the mansion system, within Chinese constallation, to define the night sky and the directions.
# Korean i18n reference: https://en.wikipedia.org/wiki/Twenty-Eight_Mansions (Traditional Chinese is used to specify)
release-name {
  Horn = "角 (각)"
  GreatHorn = "大角 (대각)"
  Executions = "折威 (절위)"
  Trials = "顿顽 1.20.1 (돈완)"
}
java {
  deprecated = [
    "오래된 JAVA 버전을 이용하고 있습니다!"
    "현재: {0}, 권장: {1}"
    "현재 Java 버전은 향후에 지원되지 않을 것입니다."
  ]
}

implementer {
  not-found = "Class를 찾을 수 없음: {}"
  error = "오류 발생: {}"
}
i18n {
  current-not-available = "현재 언어 설정 {0}을/를 이용할 수 없습니다."
  using-language = "언어 {1} 대신 {0}을 사용합니다."
}
loading-mapping = "맵핑 로드 중..."
mixin-load {
  core = "Luminara core mixin 적용됨"
  optimization = "Luminara 최적화 mixin 적용됨"
}
mod-load = "Luminara 모드 로드됨"
patcher {
  loading = "플러그인 패치기 로드 중..."
  loaded = "{} 패치기 로드 완료"
  load-error = "패치기 로드 중 오류 발생"
}
registry {
  forge-event = "Arclight 이벤트 적용됨"
  begin = "버킷에 적용 중..."
  error = "Forge 적용 중 오류 발생"
  enchantment = "인챈트({}종) 적용 완료"
  potion = "신규 물약 효과({}종) 적용 완료"
  material = "신규 자원(총 {}종: 블록 {}종, 아이템 {}종) 적용 완료"
  entity-type = "신규 엔티티({}종) 적용 완료"
  environment = "신규 차원({}종) 적용 완료"
  villager-profession = "신규 주민 직업({}종) 적용 완료"
  biome = "신규 바이옴({}종) 적용 완료"
  initialization-error = "Arclight 초기화 중 오류 발생"
  meta-type {
    not-subclass = "{} is not a subclass of {}"
    error = "{} provided itemMetaType {} is invalid: {}"
    no-candidate = "{} do not found a valid constructor in prodived itemMetaType {}"
  }
  block-state {
    not-subclass = "{} is not a subclass of {}"
    error = "{} prodived itemMetaType {} is invalid {}"
    no-candidate = "{} do not found a valid constructor in provided blockStateClass {}"
  }
  entity {
    not-subclass = "{} is not a subclass of {}"
    error = "{} prodived entityClass {} is invalid: {}"
  }
  debug {
    registered-cooking-category = "{} 를 요리 카테고리 {} 로 등록함"
    registered-spawn-category = "{} 를 스폰 카테고리 {} 로 등록함"
    registered-custom-stats = "{} 를 커스텀 통계 {} 로 등록함"
    registered-biome = "{} 를 바이옴 {} 로 등록함"
    registered-environment = "{} 를 환경 {} 로 등록함"
    registered-entity = "{} 를 엔티티 {} 로 등록함"
    registered-enchantment = "{} 를 인챈트 {} 로 등록함"
    failed-register-enchantment = "인챈트 {} 등록 실패: {}"
    registered-potion = "{} 를 포션 {} 로 등록함"
    failed-register-potion = "포션 타입 {} 등록 실패: {}"
    registered-block = "{} 를 블록 {} 로 등록함"
    registered-item = "{} 를 아이템 {} 로 등록함"
    not-found-entity = "{} 에서 {} 를 찾을 수 없음"
  }
  event-handler {
    registration-error = "이벤트 핸들러 등록 중 오류 발생: {} {}"
    invalid-signature = "{} 가 {} 에서 잘못된 EventHandler 메서드 시그니처 \"{}\" 를 등록하려고 했습니다"
    plugin-failed-register = "플러그인 {} 가 {} 에 대한 이벤트 등록에 실패했습니다. {} 가 존재하지 않기 때문입니다."
  }
  entity-mapping {
    no-valid-mapping = "{} 에 유효한 엔티티 클래스 매핑이 없습니다"
    missing-mapping-error = "유효한 엔티티 클래스 매핑이 누락되었습니다"
  }
  plugin {
    load-error-invalid-name = "플러그인 '{}' (디렉토리 {}) 로딩 중 오류: 잘못된 이름"
    load-error-space-in-name = "플러그인 '{}' (디렉토리 {}) 로딩 중 오류: 플러그인 이름에 공백 포함"
    load-error-general = "플러그인 '{}' (디렉토리 {}) 로딩 중 오류: {}"
    load-error-simple = "플러그인 '{}' (디렉토리 {}) 로딩 중 오류"
    enabling = "플러그인 {} 활성화 중"
    disabling = "플러그인 {} 비활성화 중"
  }
}

# Server initialization messages
server-init {
  failed-initialize-server = "서버 초기화 실패"
}

# Class loading and cache messages
class-cache {
  obsolete-cleared = "오래된 플러그인 클래스 캐시가 정리되었습니다"
  failed-initialize = "클래스 캐시 초기화 실패"
  failed-close = "클래스 캐시 종료 실패"
  cannot-find-package = "패키지 {}를 찾을 수 없습니다"
}

# Component bridge messages
component-bridge {
  initialized-successfully = "ComponentBridgeHandler가 메서드 {}로 성공적으로 초기화되었습니다"
  could-not-find-method = "Component 클래스에서 getSiblings 메서드를 찾을 수 없습니다"
  failed-initialize = "ComponentBridgeHandler 초기화 실패: {}"
}
error-symlink = "파일 시스템이 symbolic link를 지원하지 않습니다."
symlink-file-exist = "심볼릭 링크 {} 생성 시 파일이 이미 존재합니다"

# Server lifecycle messages
server {
  starting = "Luminara 서버 시작 중..."
  started = "Luminara 서버 시작 완료! {} 밀리초 소요"
  stopping = "Luminara 서버 종료 중..."
  stopped = "Luminara 서버가 종료되었습니다"
  crash-report-saved = "Luminara가 서버 크래시 리포트를 {}에 저장했습니다. 확인해주세요!"
  crash-report-failed = "서버 크래시 리포트를 디스크에 저장할 수 없습니다. 디스크 공간과 권한을 확인해주세요!"
  unexpected-exception = "예상치 못한 예외가 발생했습니다"
  overload-warning = "서버가 따라잡을 수 없습니다! 서버가 과부하 상태인가요? {} 밀리초 지연!"
  exception-stopping = "서버 종료 중 예외 발생"
  async-world-save-starting = "서버 종료 중 비동기 월드 저장 시작..."
  async-world-save-starting-general = "비동기 월드 저장 시작..."
  async-world-save-failed = "월드 {} 저장 실패"
  saving-chunks = "월드 '{}'의 청크 저장 중"
  minecraft-version = "클라이언트 버전 {}용 Minecraft 서버 시작 중"
  java-memory-too-low = "서버 시작을 위한 메모리가 부족합니다. 최소 1024MB를 할당해주세요! 참고: \"java -Xmx1024M -Xms1024M -jar server.jar\""
  loading-properties = "서버 설정 파일 (server.properties) 로딩 중..."
  default-gamemode = "기본 게임 모드: {}"
  starting-server = "{}:{}에서 Minecraft 서버 시작 중"
  player-auth-warning1 = "/// !!! Luminara 경고 !!! ///"
  player-auth-warning2 = "서버가 오프라인 모드로 실행 중이며 플레이어 이름을 인증하지 않습니다. 서버 보안을 확인해주세요!"
  player-auth-warning3 = "오프라인 모드는 LAN 플레이어가 인증 없이 접속할 수 있지만 보안 위험도 있습니다!"
  player-auth-warning4 = "온라인 모드를 활성화하려면 server.properties에서 \"online-mode\"를 \"true\"로 설정하세요!"
  bind-port-warning1 = "/// !!! Luminara 경고 !!! ///"
  bind-port-warning2 = "예상치 못한 상황 발생: {}"
  bind-port-warning3 = "포트 {}가 이미 다른 서버에서 사용 중일 수 있습니다!"
  start-done = "Luminara 시작 완료! 총 시간: {}, 명령어 도움말은 /help를 사용하세요!"
}

# Optimization system messages
optimization {
  chunk {
    unloading = "월드 {}의 청크 [{}, {}] 언로드 중"
    unloaded = "{} 개의 청크가 언로드되었습니다"
    rate-limit = "청크 로딩 속도 제한: 원본 {} -> 제한 {}"
  }
  memory {
    cleanup-start = "메모리 정리 시작..."
    cleanup-complete = "메모리 정리 완료, {} MB 해제됨"
    high-usage = "높은 메모리 사용량: {}%"
    gc-triggered = "가비지 컬렉션 실행됨"
    cache-cleanup-completed = "Luminara 캐시 정리 완료"
    cache-cleanup-error = "캐시 정리 중 오류 발생"
    cache-cleanup-failed = "캐시 정리 실패"
  }
  manager {
    shutdown-error = "최적화 시스템 종료 중 오류 발생"
  }
  async-ai {
    calculation-error = "비동기 AI 계산 중 오류 발생"
    processing-error = "비동기 AI에서 엔티티 {} 처리 중 오류 발생"
    ai-calculation-error = "AI 계산 중 오류 발생"
  }
  async-collision {
    calculation-error = "비동기 충돌 계산 중 오류 발생"
    processing-error = "비동기 충돌에서 엔티티 {} 처리 중 오류 발생"
    check-error = "충돌 검사 중 오류 발생"
    handling-error = "{} 와 {} 간의 비동기 충돌 처리 중 오류 발생"
    calculation-general-error = "충돌 계산 중 오류 발생"
  }
  async-event {
    disabled-due-to-errors = "오류로 인해 이벤트 타입의 비동기 처리 비활성화: {}"
    handler-error = "이벤트 {}의 비동기 이벤트 핸들러에서 오류 발생"
    registered = "비동기 이벤트 등록됨: {}"
    interrupted-shutdown = "종료 중 중단됨"
    registered-sync = "동기 이벤트 등록됨: {}"
  }
}

# Velocity forwarding messages
velocity {
  enabled = "Velocity Modern 포워딩이 활성화되었습니다"
  disabled = "Velocity Modern 포워딩이 비활성화되었습니다"
  loaded-argument-types = "{} 개의 통합 인수 타입이 로드되었습니다"
  failed-load-argument-types = "통합 인수 타입 로드 실패, 기본값 사용"
}

# Error messages
error {
  class-not-found = "클래스를 찾을 수 없음: {}"
  method-not-found = "메서드를 찾을 수 없음: {}"
  field-not-found = "필드를 찾을 수 없음: {}"
  invalid-configuration = "잘못된 설정 파일: {}"
  file-not-found = "파일을 찾을 수 없음: {}"
  permission-denied = "권한이 거부됨: {}"
  network-error = "네트워크 오류: {}"
  database-error = "데이터베이스 오류: {}"
  plugin-error = "플러그인 오류: {}"
  mixin-error = "Mixin 오류: {}"
}

# Warning messages
warning {
  deprecated-api = "사용 중단된 API 사용: {}"
  performance-issue = "성능 문제 감지: {}"
  memory-low = "메모리 부족 경고, 현재 사용량: {}%"
  disk-space-low = "디스크 공간 부족: {} MB 남음"
  plugin-conflict = "플러그인 충돌 감지: {}가 {}와 충돌할 수 있음"
  async-operation = "비동기 작업 경고: {}"
}

# World management messages
world {
  creating = "월드 {} 생성 중"
  created = "월드 {} 생성됨"
  loading = "월드 {} 로딩 중"
  loaded = "월드 {} 로드됨"
  unloading = "월드 {} 언로드 중"
  unloaded = "월드 {} 언로드됨"
  saving = "월드 저장 중: {}"
  saved-successfully = "월드 {} 저장 성공"
  save-error = "월드 {} 저장 중 오류 발생"
}

# Mod integration messages
mod {
  conflict-detected = "모드 충돌 감지: {} vs {}"
  conflict-fatal = "치명적인 모드 충돌! 이 모드들은 호환되지 않으며 서버가 중지됩니다. 모드 중 하나를 제거하고 서버를 다시 시작해주세요."
}

# Sign block entity messages
sign {
  non-editable-warning = "플레이어 {}가 편집할 수 없는 표지판을 변경하려고 했습니다"
}

# Enum extender messages
enum {
  not-found-warning = "{}에서 {}를 찾을 것으로 예상했지만 찾지 못했습니다"
}

# World symlink messages
symlink {
  create-error = "심볼릭 링크 생성 중 오류 발생"
}

# Distribution validation messages
dist {
  logic-world-check = "레벨 클래스 {}가 논리 월드로 간주됨: {}"
}

# Chat system messages
chat {
  message-too-long = "채팅 메시지가 너무 깁니다!"
  empty-message-warning = "{}가 빈 메시지를 보내려고 했습니다"
  long-message-warning = "{}가 너무 긴 메시지를 보내려고 했습니다: {} 글자"
  illegal-characters = "채팅 메시지에 불법 문자가 포함되어 있습니다"
  player-removed = "플레이어가 제거되어 메시지를 보낼 수 없습니다"
}

# Player action messages
player {
  dropped-items-quickly = "{}가 아이템을 너무 빨리 버렸습니다!"
  dropped-items-disconnect = "아이템을 너무 빨리 버렸습니다 (치팅?)"
  invalid-hotbar = "{}가 잘못된 핫바 선택을 설정하려고 했습니다"
  invalid-hotbar-disconnect = "잘못된 핫바 선택 (치팅?)"
  command-issued = "{}가 서버 명령어를 실행했습니다: {}"
  internal-command-error = "이 명령어를 실행하는 중 내부 오류가 발생했습니다"
  book-edited-quickly = "책 편집이 너무 빠릅니다!"
}

comments {
  _v.comment = [
    "레포지트리: https://github.com/QianMoo0121/Luminara"
    "이슈 트래커: https://github.com/QianMoo0121/Luminara/issues"
    ""
    ""
    "설정 버전 번호, 편집하지 마세요."
  ]
  locale.comment = "언어/I18n 설정"
  optimization {
    comment = "최적화 관련 설정"
    goal-selector-update-interval.comment = [
      "목표 선택기가 업데이트되는 시간(틱 단위)을 설정합니다."
      "높은 값은 더 적은 리소스를 소모합니다."
      "대신 몹의 목표 변경이 덜 자주 일어나게 됩니다."
    ]
  }
  async-catcher.comment = [
    "Async Catcher 관련 설정"
    "네 개의 모드가 있으며, "BLOCK"로 설정하는 것을 권장합니다."
    "NONE - 아무 것도 하지 않음"
    "DISPATCH - 주 스레드에 올리지 않고 백그라운드에서 실행"
    "BLOCK - 주 스레드에 올리고 결과를 대기"
    "EXCEPTION - 오류를 발생"
  ]
  async-catcher.dump.comment = "스택 추적 덤프 정보는 debug.log에 있습니다."
  async-world-save.comment = [
    "비동기 월드 저장 관련 설정"
    "서버 종료 시 월드 데이터를 비동기적으로 저장하여 종료 시간을 단축합니다"
  ]
  async-world-save.enabled.comment = "비동기 월드 저장 기능을 활성화할지 여부"
  async-world-save.timeout-seconds.comment = [
    "비동기 저장 타임아웃 시간(초)"
    "이 시간 내에 저장이 완료되지 않으면 서버는 종료 프로세스를 계속합니다"
  ]
  async-world-save.save-world-data.comment = "비동기 저장에 월드 데이터를 포함할지 여부"
  compatibility {
    symlink-world.comment = [
      "버킷 포맷에 맞도록 모드 차원 폴더에 대한 Symbolic Link를 생성합니다."
      "활성화하면 플러그인과의 호환성을 향상시킬 수 있습니다."
      "이 설정을 수정하면 모드의 월드 명칭이 바뀌어 월드 명칭에 의존하는 플러그인의"
      "  데이터 손실이 발생할 수 있습니다."
      "https://github.com/IzzelAliz/Arclight/wiki/World-Symlink (영문)에서 자세한"
      "  내용을 확인하세요."
    ]
    extra-logic-worlds.comment = [
      "별도 세계 작동 로직"
      "제대로 작동하지 않는 모드가 있다면, 로그 파일에서 [EXT_LOGIC]와 관련된 클래스 명을"
      "  찾아 여기에 추가해 주세요."
    ]
    forward-permission.comment = [
      "true - Forge의 권한 쿼리를 Bukkit으로 넘깁니다."
      "false - 권한 넘기기를 비활성화합니다."
      "reverse - Bukkit의 권한 쿼리를 Forge로 넘깁니다."
    ]
    valid-username-regex.comment = [
      "유효한 사용자명 확인을 위한 정규식. 비워두면 바닐라 확인을 사용합니다"
      "다음은 중국어 문자를 허용합니다:"
      "valid-username-regex = \"^[ -~\\\\p{sc=Han}]{1,16}$\""
      "다음은 모든 사용자명의 로그인을 허용합니다:"
      "valid-username-regex = \".+\""
    ]
    lenient-item-tag-match.comment = [
      "빈 nbt 태그가 있는 아이템이 태그가 없는 아이템과 스택되도록 허용"
    ]
  }
  velocity {
    comment = "Velocity Modern Forwarding 관련 설정"
    enabled.comment = [
      "Velocity Modern Forwarding 지원을 활성화할지 여부"
      "활성화하면 Velocity 프록시 서버와의 통합이 가능합니다"
    ]
    online-mode.comment = [
      "온라인 모드 검증을 활성화할지 여부"
      "일반적으로 Velocity 설정의 online-mode 설정과 일치해야 합니다"
    ]
    forwarding-secret.comment = [
      "Velocity 포워딩 비밀 키"
      "Velocity 설정 파일의 forwarding-secret와 정확히 일치해야 합니다"
      "Velocity로부터의 연결 요청을 인증하는 데 사용됩니다"
    ]
    debug-logging.comment = [
      "로그에 Velocity 포워딩 관련 디버그 정보를 표시할지 여부"
      "연결 문제 진단에 도움이 되도록 이 기능을 활성화하세요"
    ]
  }
}
